import { Typography } from '@bratislava/component-library'
import { useControlledState } from '@react-stately/utils'
import { rawRequest } from 'graphql-request'
import { useTranslation } from 'next-i18next'
import React, { useState } from 'react'

import { ArrowLeftIcon, ArrowRightIcon } from '@/src/assets/icons'
import Button from '@/src/components/common/Button/Button'
import Input from '@/src/components/common/Input/Input'
import { PaginationProps } from '@/src/components/common/Pagination/Pagination'
import cn from '@/src/utils/cn'

/**
 * prevent "up arrow" key reseting cursor position within textbox
 * @see https://stackoverflow.com/a/1081114
 */
const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
  // if (event.keyCode === KeyCode.UP || event.keyCode === KeyCode.DOWN) {
  event.preventDefault()
  // }
}

/**
 * Figma: https://www.figma.com/design/2qF09hDT9QNcpdztVMNAY4/OLO-Web?node-id=37-1906&t=Ix6vxd23ycmma0c2-4
 * Interaction design inspired by: https://ant.design/components/pagination-cn, https://github.dev/react-component/pagination
 */

const PaginationWithInput = ({
  currentPage,
  totalCount,
  onPageChange: handlePageChange,
}: PaginationProps) => {
  const { t } = useTranslation()

  const [internalCurrentPage] = useControlledState<number>(currentPage, 1)
  const [inputValue, setInputValue] = useState(
    Math.max(1, Math.min(internalCurrentPage, totalCount)),
  )

  const getValidValue = (originalValue: any): number => {
    let validValue: number
    if (originalValue === '') {
      validValue = originalValue
    } else if (Number.isNaN(Number(originalValue))) {
      validValue = internalCurrentPage
    } else if (originalValue >= totalCount) {
      validValue = totalCount
    } else {
      validValue = Number(originalValue)
    }
    console.log('getValidvalue function - rawValue =', originalValue)
    console.log('getValidvalue function - return value =', validValue)

    return validValue
  }

  const handleKeyUp = (event: any) => {
    const value = getValidValue(event.target?.value)
    if (value !== internalCurrentPage) {
      setInputValue(value)
    }

    console.log('EVENT KEY:', event.key)
    console.log('EVENT target:', event.target.value)

    switch (event.key) {
      case 'Enter':
        handlePageChange(value)
        break

      case 'ArrowDown':
        handlePageChange(value - 1)
        break

      case 'ArrowUp':
        handlePageChange(value + 1)
        break

      default:
        break
    }
  }

  const handleChange = (page: number) => {
    let newPage = page
    if (page > currentPage) {
      newPage = currentPage
    } else if (page < 1) {
      newPage = 1
    }

    if (newPage !== inputValue) {
      setInputValue(newPage)
    }

    handlePageChange(newPage)
    onChange?.(newPage, pageSize)

    return newPage

    return current
  }

  const handleBlur = (event: React.FocusEvent<HTMLInputElement, Element>) => {
    handlePageChange(getValidValue(event.target.value))
  }

  return (
    <nav>
      {JSON.stringify({ currentPage, internalCurrentPage, inputValue })}
      <div className={cn('flex items-center justify-start gap-4')}>
        <Button
          variant="plain"
          isDisabled={Number(inputValue) < 2}
          // TODO:
          onPress={() => {}}
          aria-label={t('Pagination.aria.goToPreviousPage', inputValue.toString())}
          icon={<ArrowLeftIcon />}
          className="rounded-full"
        />

        <div className="flex items-center justify-center gap-2">
          <Input
            type="number"
            aria-label={t('Pagination.aria.goToPage', { page: inputValue })}
            value={inputValue}
            onKeyDown={handleKeyDown}
            onKeyUp={handleKeyUp}
            onChange={handleKeyUp}
            onBlur={handleBlur}
            className="items-center justify-center"
            // classNameInner={cn('w-15 text-center', {
            //   // Widen the input field slightly for more than 3 digits
            //   'w-[4.37rem]': inputValue.toString().length > 3,
            // })}
          />

          <div className="flex gap-1">
            <div className="flex size-6 justify-center">
              <Typography variant="p-default">/</Typography>
            </div>
            <Typography variant="p-default">{totalCount}</Typography>
          </div>
        </div>

        <Button
          variant="plain"
          isDisabled={Number(inputValue) >= totalCount || inputValue.toString() === ''}
          // TODO:
          onPress={() => {}}
          aria-label={t('Pagination.aria.goToNextPage', inputValue.toString())}
          icon={<ArrowRightIcon />}
          className="rounded-full"
        />
      </div>
    </nav>
  )
}

export default PaginationWithInput
