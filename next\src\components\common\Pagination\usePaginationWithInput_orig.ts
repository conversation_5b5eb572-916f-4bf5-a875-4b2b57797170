import { ChangeEvent, KeyboardEvent, useEffect, useState } from 'react'

/**
 * Inspired by: https://github.dev/react-component/pagination
 */

export const usePaginationWithInput = (
  currentPage: number,
  totalCount: number,
  handlePageChange: ((value: number) => void) | undefined,
) => {
  const [inputValue, setInputValue] = useState<string | number>(currentPage)

  useEffect(() => {
    setInputValue(currentPage)
  }, [currentPage, setInputValue]) // Triggered when currentPage gets updated in parent component (e.g., DocumentsSectionAll)

  const handleIncrement = (userInput: number) => {
    const newUserInputValue = userInput + 1 === totalCount ? totalCount : userInput + 1
    handlePageChange?.(newUserInputValue)
  }

  const handleDecrement = (userInput: number) => {
    const newUserInputValue = userInput - 1 === 0 ? 1 : userInput - 1
    handlePageChange?.(newUserInputValue)
  }

  // Functions for handling keyboard input

  // eslint-disable-next-line unicorn/consistent-function-scoping
  const sanitizePaginationInput = (userInput: string | number, totalPageCount: number): number => {
    const numericUserInput = Number(userInput)

    if (userInput === '' || numericUserInput < 1) {
      return 1 // Ensure that entering a number less than 1 or leaving the input empty defaults to the first page
    }

    if (numericUserInput > totalPageCount) {
      return totalPageCount
    }

    return numericUserInput
  }

  const handleInputChange = (event: ChangeEvent<HTMLInputElement>) =>
    setInputValue(sanitizePaginationInput(event.target.value, totalCount))

  const handleBlur = () => {
    setInputValue(sanitizePaginationInput(inputValue, totalCount))
    handlePageChange?.(sanitizePaginationInput(inputValue, totalCount))
  }

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    if (['e', 'E', '+', '-', '.', ','].includes(event.key)) {
      event.preventDefault() // Restrict input type `number` to integers only
    }

    if (event.key === 'Enter') {
      handleBlur()
    }

    if (event.key === 'Delete' || event.key === 'Backspace') {
      setInputValue('')
    }
  }

  return {
    inputValue,
    handleInputChange,
    handleDecrement,
    handleIncrement,
    handleBlur,
    handleKeyDown,
  }
}
